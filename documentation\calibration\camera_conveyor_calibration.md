# Camera Conveyor Calibration
1. Put the conveyor in maintenance mode (by running `code/dev_scripts/conveyor_maintenance_mode_enable.py`)
2. Start up `code/calibration/camera_conveyor_calibration.py`.
3. Use aruco markers, place them in the field of view of the input camera on the conveyor. Use the marker ID's coresponding to those in the `code/config/conveyor.toml` file.
4. Place the upstream marker on the right side (looking with the movement direction), where you want the camera to start looking.
5. Place the downstream marker on the right side where you want the camera to stop looking.
6. Place the width marker on the left side, between the upstream and downstream marker along the movement direction.
7. The orientation of the markers is important. One of the corners of the marker is taken as reference point. This point is indicated red in the interactive visualisation.
   - The red corner of the upstream marker marks:
     - The outer edge of the ROI.
     - The earliest line the camera will look at.
   - The red corner of the width marker marks:
     - The width of the conveyor.
   - The red corner of the downstream marker marks:
     - The direction of the conveyor (with respect to the upstream marker)
     - The latest line the camera will look at.
8. When you are happy with the placement, press space and wait for the 100 frames to be recorded. Don't obstruct the view or touch the setup during this fase.
9.  A visualisation will pop up. Check if the ROI is correct and the red axis is aligned with the movement direction and points downstream. The green axis should point across the conveyor and the blue axis should point upwards.
10. Check if the ROI indicated as a white wireframe is correct.
11. Press escape to close the visualisation.
12. In the console, check the resulting measurements, warnings and errors may pop up if the measurements are suspicious.
13. If everything is correct, press `y` and enter to save the calibration to `code/config/conveyor.toml`,  otherwise press `n` and enter to discard the calibration and try again from step 1.
14. Reset conveyor to working mode (by running `code/dev_scripts/conveyor_maintenance_mode_disable.py`)
