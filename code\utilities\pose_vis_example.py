"""
Example usage of the pose visualization utilities.

This script demonstrates how to use the pose_visualizer module to visualize
3D poses in various scenarios.
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# Add the parent directory to the path to import pose
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from pose import Pose
from utilities.pose_visualizer import vis_pose, vis_multiple_poses, vis_pose_trajectory


def example_robot_poses():
    """Example: Visualizing robot poses for pick and place operation"""
    print("Example: Robot Pick and Place Poses")
    
    # Define some typical robot poses
    home_pose = Pose.from_xyz_rotvec([0, 0, 0.5, 0, 0, 0])  # Home position
    approach_pose = Pose.from_xyz_rotvec([0.3, 0.2, 0.2, 0, np.pi/2, 0])  # Approach
    pick_pose = Pose.from_xyz_rotvec([0.3, 0.2, 0.05, 0, np.pi/2, 0])  # Pick
    place_pose = Pose.from_xyz_rotvec([-0.3, 0.2, 0.1, 0, np.pi/2, np.pi])  # Place
    
    poses = [home_pose, approach_pose, pick_pose, place_pose]
    labels = ["Home", "Approach", "Pick", "Place"]
    
    vis_multiple_poses(poses, labels=labels, scale=0.1, title="Robot Pick and Place Sequence")
    plt.show()


def example_camera_poses():
    """Example: Visualizing camera poses around an object"""
    print("Example: Camera Poses Around Object")
    
    # Create camera poses in a circle around origin, looking inward
    camera_poses = []
    n_cameras = 8
    radius = 1.0
    height = 0.5
    
    for i in range(n_cameras):
        angle = 2 * np.pi * i / n_cameras
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)
        z = height
        
        # Camera looks toward center (rotation around Z then Y)
        yaw = angle + np.pi  # Point toward center
        pitch = -np.arctan2(height, radius)  # Look down toward center
        roll = 0
        
        pose = Pose.from_xyz_euler([x, y, z, roll, pitch, yaw], seq='xyz')
        camera_poses.append(pose)
    
    labels = [f"Cam {i+1}" for i in range(n_cameras)]
    vis_multiple_poses(camera_poses, labels=labels, scale=0.15, 
                      title="Camera Poses Around Object")
    plt.show()


def example_trajectory():
    """Example: Visualizing a smooth trajectory"""
    print("Example: Smooth Trajectory")
    
    # Create a helical trajectory
    t_values = np.linspace(0, 4*np.pi, 50)
    trajectory_poses = []
    
    for t in t_values:
        # Helical path
        x = 0.5 * np.cos(t)
        y = 0.5 * np.sin(t)
        z = t * 0.05
        
        # Rotating orientation
        rx = np.sin(t * 0.5) * 0.3
        ry = np.cos(t * 0.3) * 0.2
        rz = t * 0.1
        
        pose = Pose.from_xyz_rotvec([x, y, z, rx, ry, rz])
        trajectory_poses.append(pose)
    
    vis_pose_trajectory(trajectory_poses, scale=0.06, 
                       title="Helical Trajectory with Varying Orientation")
    plt.show()


def example_pose_interpolation():
    """Example: Visualizing pose interpolation (SLERP)"""
    print("Example: Pose Interpolation")
    
    # Define start and end poses
    start_pose = Pose.from_xyz_rotvec([0, 0, 0, 0, 0, 0])
    end_pose = Pose.from_xyz_rotvec([1, 1, 0.5, np.pi/3, np.pi/6, np.pi/2])
    
    # Create interpolated poses
    alphas = np.linspace(0, 1, 10)
    interpolated_poses = start_pose.slerp(end_pose, alphas)
    
    labels = [f"α={alpha:.1f}" for alpha in alphas]
    vis_multiple_poses(interpolated_poses, labels=labels, scale=0.08,
                      title="Pose Interpolation (SLERP)")
    plt.show()


def example_coordinate_frames():
    """Example: Visualizing different coordinate frame conventions"""
    print("Example: Different Coordinate Frame Conventions")
    
    # Create poses representing different coordinate systems
    # Standard right-handed coordinate system
    standard = Pose.identity()
    
    # Rotated coordinate systems
    x_90 = Pose.from_xyz_euler([0.3, 0, 0, 90, 0, 0], seq='xyz', degrees=True)
    y_90 = Pose.from_xyz_euler([0, 0.3, 0, 0, 90, 0], seq='xyz', degrees=True)
    z_90 = Pose.from_xyz_euler([0, 0, 0.3, 0, 0, 90], seq='xyz', degrees=True)
    
    poses = [standard, x_90, y_90, z_90]
    labels = ["Standard", "X+90°", "Y+90°", "Z+90°"]
    
    vis_multiple_poses(poses, labels=labels, scale=0.15,
                      title="Different Coordinate Frame Orientations")
    plt.show()


def main():
    """Run all examples"""
    print("=== Pose Visualization Examples ===\n")
    
    example_robot_poses()
    example_camera_poses()
    example_trajectory()
    example_pose_interpolation()
    example_coordinate_frames()
    
    print("\nAll examples completed!")


if __name__ == "__main__":
    main()
