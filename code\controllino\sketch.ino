#include <WiFiNINA.h>

const char *ssid = "YOUR_WIFI_SSID";
const char *password = "YOUR_WIFI_PASSWORD";

WiFiServer server(80);

void setup()
{
  Serial.begin(9600);

  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED)
  {
    delay(1000);
    Serial.print(".");
  }

  server.begin();
  Serial.print("Server started at: ");
  Serial.println(WiFi.localIP());
}

void loop()
{
  WiFiClient client = server.available();
  if (!client)
    return;

  String request = client.readStringUntil('\r');
  client.flush();

  String response = "";
  int statusCode = 404;

  if (request.indexOf("GET /heartbeat") >= 0)
  {
    response = "{\"status\":\"alive\"}";
    statusCode = 200;
  }
  else if (request.indexOf("GET /relay?pin=") >= 0)
  {
    int pinStart = request.indexOf("pin=") + 4;
    int pinEnd = request.indexOf(" ", pinStart);
    if (pinEnd == -1)
      pinEnd = request.indexOf("&", pinStart);
    if (pinEnd == -1)
      pinEnd = request.length();

    int pin = request.substring(pinStart, pinEnd).toInt();
    if (pin >= 0 && pin <= 21)
    {
      pinMode(pin, INPUT);
      bool state = digitalRead(pin);
      response = "{\"pin\":" + String(pin) + ",\"state\":" + String(state ? "true" : "false") + "}";
      statusCode = 200;
    }
    else
    {
      response = "{\"error\":\"Invalid pin\"}";
      statusCode = 400;
    }
  }
  else if (request.indexOf("POST /relay?pin=") >= 0)
  {
    int pinStart = request.indexOf("pin=") + 4;
    int stateStart = request.indexOf("state=") + 6;

    int pin = request.substring(pinStart, request.indexOf("&")).toInt();
    String stateStr = request.substring(stateStart, request.indexOf(" ", stateStart));

    if (pin >= 0 && pin <= 21 && (stateStr == "1" || stateStr == "0"))
    {
      pinMode(pin, OUTPUT);
      bool state = (stateStr == "1");
      digitalWrite(pin, state);
      response = "{\"pin\":" + String(pin) + ",\"state\":" + String(state ? "true" : "false") + "}";
      statusCode = 200;
    }
    else
    {
      response = "{\"error\":\"Invalid parameters\"}";
      statusCode = 400;
    }
  }

  client.println("HTTP/1.1 " + String(statusCode) + (statusCode == 200 ? " OK" : (statusCode == 400 ? " Bad Request" : " Not Found")));
  client.println("Content-Type: application/json");
  client.println("Connection: close");
  client.println();
  client.println(response);

  client.stop();
}
