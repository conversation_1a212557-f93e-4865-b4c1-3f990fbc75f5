#!/bin/bash

# FRAMOS Camera Recovery Script
# Use this when camera is lost after disconnecting/reconnecting LAN cable

echo "=== FRAMOS Camera Recovery ==="
echo "Recovering camera connectivity on eno1 interface..."

# Flush ARP table for camera subnet
echo "1. Flushing ARP table..."
sudo ip neigh flush dev eno1

# Restart network interface
echo "2. Restarting network interface..."
sudo ip link set eno1 down
sleep 2
sudo ip link set eno1 up

# Wait for interface to come up
echo "3. Waiting for interface to stabilize..."
sleep 5

# Check interface status
echo "4. Interface status:"
ip link show eno1

# Try to ping camera
echo "5. Testing camera connectivity..."
if ping -c 3 -W 2 ************* >/dev/null 2>&1; then
    echo "✓ Camera is reachable at *************"
else
    echo "✗ Camera not responding to ping"
fi

echo ""
echo "You can now run: /usr/src/framos/camerasuite/Tools/ConfigureIp"
