import asyncio
from contextlib import asynccontextmanager
from enum import Enum
from typing import TypedDict

from loguru import logger
from pymodbus.client import AsyncModbusTcpClient
from pymodbus.exceptions import ModbusException

from conveyor import Zone


class ZoneStatus(Enum):
    CLEAR_STOPPED = 0x01
    CLEAR_RUNNING = 0x02  # accepting
    BLOCKED_RUNNING = 0x04  # discharging
    BLOCKED_STOPPED = 0x05
    BUSY = 0x06


class ModuleStatus(TypedDict):
    reset_flag: bool
    over_voltage: bool
    left_motor_error: bool
    ethernet_error: bool
    upstream_jam_error: bool
    left_sensor_error: bool
    low_voltage: bool
    left_motor_overheated: bool
    left_motor_overcurrent: bool
    left_motor_short_circuit: bool
    left_motor_not_connected: bool
    left_motor_overload: bool
    left_motor_stalled: bool
    left_motor_hall_sensor_error: bool
    left_motor_not_used: bool
    right_motor_error: bool
    downstream_jam_error: bool
    right_sensor_error: bool
    right_motor_overheated: bool
    right_motor_overcurrent: bool
    right_motor_short_circuit: bool
    right_motor_not_connected: bool
    right_motor_overload: bool
    right_motor_stalled: bool
    right_motor_hall_sensor_error: bool
    right_motor_not_used: bool


class ConveyorStopStatus(TypedDict):
    stop_active_on_other_module: bool
    stop_active_due_to_lost_communication: bool
    stop_active_due_to_lost_plc_connection: bool
    stop_active_due_to_stop_command: bool


class ConveyLinx:
    def __init__(self, ip: str, port: int = 502, timeout: float = 3.0):
        self.ip = ip
        self.port = port
        self.client = AsyncModbusTcpClient(self.ip, port=self.port, timeout=timeout)

    @asynccontextmanager
    async def connect(self):
        try:
            await self.client.connect()
            yield self
        finally:
            self.client.close()

    async def get_local_status(self, zone: Zone) -> ZoneStatus:
        if zone == Zone.UPSTREAM:
            status = await self._read_byte(115, 0)
        else:
            status = await self._read_byte(195, 0)
        return ZoneStatus(status)

    async def get_local_arrival_count(self, zone: Zone) -> int:
        if zone == Zone.UPSTREAM:
            return await self._read_register(105)
        else:
            return await self._read_register(185)

    async def get_local_departure_count(self, zone: Zone) -> int:
        if zone == Zone.UPSTREAM:
            return await self._read_register(106)
        else:
            return await self._read_register(186)

    async def get_module_status(self) -> ModuleStatus:
        word1 = await self._read_register(87)
        word2 = await self._read_register(88)
        return {
            "reset_flag": get_bit(word1, 0),
            "over_voltage": get_bit(word1, 2),
            "left_motor_error": get_bit(word1, 3),
            "ethernet_error": get_bit(word1, 4),
            "upstream_jam_error": get_bit(word1, 5),
            "left_sensor_error": get_bit(word1, 6),
            "low_voltage": get_bit(word1, 7),
            "left_motor_overheated": get_bit(word1, 8),
            "left_motor_overcurrent": get_bit(word1, 9),
            "left_motor_short_circuit": get_bit(word1, 10),
            "left_motor_not_connected": get_bit(word1, 11),
            "left_motor_overload": get_bit(word1, 12),
            "left_motor_stalled": get_bit(word1, 13),
            "left_motor_hall_sensor_error": get_bit(word1, 14),
            "left_motor_not_used": get_bit(word1, 15),
            "right_motor_error": get_bit(word2, 3),
            "downstream_jam_error": get_bit(word2, 5),
            "right_sensor_error": get_bit(word2, 6),
            "right_motor_overheated": get_bit(word2, 8),
            "right_motor_overcurrent": get_bit(word2, 9),
            "right_motor_short_circuit": get_bit(word2, 10),
            "right_motor_not_connected": get_bit(word2, 11),
            "right_motor_overload": get_bit(word2, 12),
            "right_motor_stalled": get_bit(word2, 13),
            "right_motor_hall_sensor_error": get_bit(word2, 14),
            "right_motor_not_used": get_bit(word2, 15),
        }

    async def get_local_tracking(self, zone: Zone):
        if zone == Zone.UPSTREAM:
            word1 = await self._read_register(118)
            word2 = await self._read_register(119)
        else:
            word1 = await self._read_register(198)
            word2 = await self._read_register(199)
        return word1, word2

    async def get_left_sensor_pin2(self) -> bool:
        return await self._read_bit(34, 0)

    async def get_right_sensor_pin2(self) -> bool:
        return await self._read_bit(34, 2)

    async def get_left_sensor_pin4(self) -> bool:
        return await self._read_bit(34, 4)

    async def get_right_sensor_pin4(self) -> bool:
        return await self._read_bit(34, 6)

    async def get_heartbeat(self) -> bool:
        return await self._read_bit(34, 15)

    async def get_convey_stop_status(self) -> ConveyorStopStatus:
        word = await self._read_register(18)
        return {
            "stop_active_on_other_module": get_bit(word, 5),
            "stop_active_due_to_lost_communication": get_bit(word, 6),
            "stop_active_due_to_lost_plc_connection": get_bit(word, 7),
            "stop_active_due_to_stop_command": get_bit(word, 10),
        }

    async def set_accumulation(self, zone: Zone, enable=True) -> None:
        if zone == Zone.UPSTREAM:
            await self._write_bit(103, 0, enable)
        else:
            await self._write_bit(183, 0, enable)

    async def jog_forward(self, zone: Zone, enable=True) -> None:
        if zone == Zone.UPSTREAM:
            await self._write_bit(103, 10, enable)
        else:
            await self._write_bit(183, 10, enable)

    async def jog_reverse(self, zone: Zone, enable=True) -> None:
        if zone == Zone.UPSTREAM:
            await self._write_bit(103, 11, enable)
        else:
            await self._write_bit(183, 11, enable)

    async def wake(self, zone: Zone) -> None:
        if zone == Zone.UPSTREAM:
            await self._write_bit(103, 12, True)
        else:
            await self._write_bit(183, 12, True)

    async def set_maintenance_mode(self, zone: Zone, enable=True) -> None:
        if zone == Zone.UPSTREAM:
            await self._write_bit(103, 13, enable)
        else:
            await self._write_bit(183, 13, enable)

    async def get_maintenance_mode(self, zone: Zone) -> bool:
        if zone == Zone.UPSTREAM:
            return await self._read_bit(103, 13)
        else:
            return await self._read_bit(183, 13)

    async def set_motor_speed(self, speed: int) -> None:
        """Set motor speed for both zones in mm/s."""
        await self._write_register(39, speed)
        await self._write_register(63, speed)

    async def release_and_accumulate(self, zone: Zone) -> None:
        if zone == Zone.UPSTREAM:
            value = await self._read_register(104)
            await self._write_register(104, value ^ 0b1)
        else:
            value = await self._read_register(184)
            await self._write_register(184, value ^ 0b1)

    async def upstream_induct(self, enable: bool, tracking_word_1: int = 0, tracking_word_2: int = 0) -> None:
        # FIXME: see page 254 of manual: value should be set to 1 after box clears sensor of previous conveyor
        if not (tracking_word_1 == 0 and tracking_word_2 == 0):
            # Induct with tracking
            await self._write_register(138, tracking_word_1)
            await self._write_register(139, tracking_word_2)
            await self._write_register(133, 1)

        if enable:
            await self._write_register(133, 4)
        else:
            await self._write_register(133, 0)
        return

    async def set_conveystop(self, enable: bool) -> None:
        if enable:
            await self._write_register(19, 1)
            await self._write_register(19, 0)
        else:
            await self._write_register(19, 2)
            await self._write_register(19, 0)

    async def _read_register(self, address) -> int:
        rr = await self.client.read_holding_registers(address)
        if rr.isError():
            raise ModbusException(rr)
        return rr.registers[0]

    async def _read_bit(self, register_address: int, bit_index: int) -> bool:
        value = await self._read_register(register_address)
        return get_bit(value, bit_index)

    async def _read_byte(self, register_address: int, byte_index: int) -> int:
        value = await self._read_register(register_address)
        return (value >> (byte_index * 8)) & 0xFF

    async def _write_register(self, address: int, value: int) -> None:
        rq = await self.client.write_register(address, value)
        if rq.isError():
            raise ModbusException(rq)

    async def _write_bit(self, register_address: int, bit_index: int, value: bool) -> None:
        current = await self._read_register(register_address)
        if value:
            current |= 1 << bit_index
        else:
            current &= ~(1 << bit_index)
        await self._write_register(register_address, current)


def get_bit(number: int, bit: int) -> bool:
    return bool((number >> bit) & 1)


async def main():
    # async with ConveyLinx("*************").connect() as conveyor:
    #     logger.debug("Connected to {}", conveyor.ip)
    #     logger.debug("Local upstream status: {}", await conveyor.get_local_status(Zone.UPSTREAM))
    #     logger.debug("Local downstream status: {}", await conveyor.get_local_status(Zone.DOWNSTREAM))
    #     logger.debug("Local upstream arrival count: {}", await conveyor.get_local_arrival_count(Zone.UPSTREAM))
    #     logger.debug("Local downstream arrival count: {}", await conveyor.get_local_arrival_count(Zone.DOWNSTREAM))
    #     logger.debug("Local upstream departure count: {}", await conveyor.get_local_departure_count(Zone.UPSTREAM))
    #     logger.debug("Local downstream departure count: {}", await conveyor.get_local_departure_count(Zone.DOWNSTREAM))
    #     logger.debug("Module status: {}", await conveyor.get_module_status())
    #     logger.debug("Local upstream tracking: {}", await conveyor.get_local_tracking(Zone.UPSTREAM))
    #     logger.debug("Local downstream tracking: {}", await conveyor.get_local_tracking(Zone.DOWNSTREAM))
    #     logger.debug("Left sensor pin 2: {}", await conveyor.get_left_sensor_pin2())
    #     logger.debug("Right sensor pin 2: {}", await conveyor.get_right_sensor_pin2())
    #     logger.debug("Left sensor pin 4: {}", await conveyor.get_left_sensor_pin4())
    #     logger.debug("Right sensor pin 4: {}", await conveyor.get_right_sensor_pin4())
    #     logger.debug("Heartbeat: {}", await conveyor.get_heartbeat())
    #     logger.debug("ConveyStop status: {}", await conveyor.get_convey_stop_status())

    async with ConveyLinx("*************").connect() as conveyor1, ConveyLinx("192.168.21.21").connect() as conveyor2:
        # for _ in range(5):
        #     await conveyor1.set_motor_speed(200)
        #     await conveyor2.set_motor_speed(200)
        #     await conveyor1.jog_forward(Zone.UPSTREAM, True)
        #     await conveyor1.jog_forward(Zone.DOWNSTREAM, True)
        #     await conveyor2.jog_forward(Zone.UPSTREAM, True)
        #     await conveyor2.jog_forward(Zone.DOWNSTREAM, True)
        #     await asyncio.sleep(1)
        #     await conveyor1.jog_forward(Zone.UPSTREAM, False)
        #     await conveyor1.jog_forward(Zone.DOWNSTREAM, False)
        #     await conveyor2.jog_forward(Zone.UPSTREAM, False)
        #     await conveyor2.jog_forward(Zone.DOWNSTREAM, False)
        #     await conveyor1.jog_reverse(Zone.UPSTREAM, True)
        #     await conveyor1.jog_reverse(Zone.DOWNSTREAM, True)
        #     await conveyor2.jog_reverse(Zone.UPSTREAM, True)
        #     await conveyor2.jog_reverse(Zone.DOWNSTREAM, True)
        #     await asyncio.sleep(1)
        #     await conveyor1.jog_reverse(Zone.UPSTREAM, False)
        #     await conveyor1.jog_reverse(Zone.DOWNSTREAM, False)
        #     await conveyor2.jog_reverse(Zone.UPSTREAM, False)
        #     await conveyor2.jog_reverse(Zone.DOWNSTREAM, False)
        # await conveyor2.set_accumulation(Zone.DOWNSTREAM, True)
        await conveyor2.release_and_accumulate(Zone.DOWNSTREAM)


if __name__ == "__main__":
    asyncio.run(main())
