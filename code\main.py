#!/usr/bin/env python3
"""
Main application demonstrating the box detection system.

This is a simple, clean example showing how all components work together
to detect boxes from a camera feed with optional visualization.
"""

import asyncio
import argparse
import sys
from pprint import pprint
from loguru import logger

from calibration.conveyor_robot_transformation import get_box_in_zone_pick_pose
from camera.camera_realsense import build_production as build_camera
from box_top.box_top_detector import BoxTopDetector
from box_top.box_detector import BoxDetector
from box_top.visualizer import Open3DVisualizer, NoOpVisualizer
from pose import Pose
from robot.yaskawa import build_production as build_robot


def create_visualizer(enable_visualization: bool):
    """Create the appropriate visualizer based on user preference."""
    if enable_visualization:
        return Open3DVisualizer()
    else:
        return NoOpVisualizer()


async def run_box_detection(enable_visualization: bool = True):
    """
    Run the complete box detection pipeline.

    Args:
        enable_visualization: Whether to show the 3D visualization
    """
    logger.info("🚀 Starting Box Detection System")

    # Create components
    logger.info("📹 Initializing camera...")
    camera = build_camera()

    logger.info("👁️  Setting up visualizer...")
    visualizer = create_visualizer(enable_visualization)

    logger.info("🔍 Creating box top detector...")
    box_top_detector = BoxTopDetector(camera, visualizer=visualizer)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(box_top_detector)

    logger.info("🤖 Connecting to robot...")
    robot = build_robot()

    # Run detection
    detected_count = 0
    try:
        async with robot.connect():
            logger.info("✨ Starting detection pipeline...")
            logger.info("Press Ctrl+C to stop, or ESC in visualization window")

            for detected_box in box_detector.detect_boxes():
                if detected_box is not None:
                    detected_count += 1

                    logger.info(
                        f"📦 Box #{detected_count} detected!"
                        f"   Position y: [{detected_box.top.obb_center.y * 1000:3.0f}]"
                        f"   Angle: {detected_box.top.obb_center.as_xyz_euler('xyz', degrees=True)[5] % 180:3.0f}°"
                        f"   Size: {detected_box.obb_extent[0] * 1000:3.0f} x {detected_box.obb_extent[1] * 1000:3.0f} x {detected_box.obb_extent[2] * 1000:3.0f} mm"
                    )
                    pprint(detected_box)

                    pick_pose = get_box_in_zone_pick_pose(2, detected_box)
                    logger.info(f"   Pick pose: {pick_pose.as_xyz_euler('xyz', degrees=True)}")
                    await robot.touch_vertically(
                        pick_pose,
                        speed=0.03,
                    )

    except KeyboardInterrupt:
        logger.info("⏹️  Detection stopped by user")
    except Exception as e:
        logger.trace(f"❌ Error during detection: {e}")
        raise
    finally:
        logger.info(f"✅ Detection completed. Total boxes detected: {detected_count}")


def run_mock_detection():
    """
    Run box detection with mock data for testing/demo purposes.
    """
    logger.info("🎭 Starting Mock Box Detection System")

    from box_top.box_top_detector_mock import BoxTopDetectorMock

    # Create mock components
    logger.info("📁 Loading mock data...")
    mock_detector = BoxTopDetectorMock("../data/box_top_recording_01.txt", loop=False)

    logger.info("🎯 Creating box detector...")
    box_detector = BoxDetector(mock_detector)

    # Run detection
    detected_count = 0

    try:
        logger.info("✨ Starting mock detection...")

        for detected_box in box_detector.detect_boxes():
            if detected_box is not None:
                detected_count += 1

                logger.info(
                    f"📦 Box #{detected_count} detected!"
                    f"   Position y: {detected_box.top.obb_center.y * 1000:.0f} mm"
                    f"   Angle: {detected_box.top.obb_center.as_xyz_euler('xyz', degrees=True)[5] % 180:.0f}°"
                    f"   Top size: {detected_box.obb_extent[0] * 1000:.0f} x {detected_box.obb_extent[1] * 1000:.0f} mm"
                )

    except KeyboardInterrupt:
        logger.info("⏹️  Mock detection stopped by user")
    except Exception as e:
        logger.error(f"❌ Error during mock detection: {e}")
        raise
    finally:
        logger.info(f"✅ Mock detection completed. Total boxes detected: {detected_count}")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Box Detection System - Detect boxes from camera or mock data",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Run with camera and visualization
  python main.py --no-viz           # Run with camera, no visualization
  python main.py --mock             # Run with mock data
        """,
    )

    parser.add_argument("--mock", action="store_true", help="Use mock data instead of real camera")

    parser.add_argument("--no-viz", action="store_true", help="Disable 3D visualization (headless mode)")

    args = parser.parse_args()

    try:
        if args.mock:
            run_mock_detection()
        else:
            asyncio.run(run_box_detection(enable_visualization=not args.no_viz))

    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
