from dataclasses import dataclass
from typing import Iterable, List, Literal, Sequence, Tuple, Union, overload

import numpy as np
from numpy.typing import NDArray
from scipy.spatial.transform import Rotation as Rot
from scipy.spatial.transform import Slerp
from typing_extensions import Self

ArrayLike = Union[NDArray[np.floating], Iterable[float], Sequence[float]]
_RotAxisSeq = Literal[
    "xyz", "xzy", "yxz", "yzx", "zxy", "zyx",
    "xyx", "xzx", "yxy", "yzy", "zxz", "zyz",
    "XYZ", "XZY", "YXZ", "YZX", "ZXY", "ZYX",
    "XYX", "XZX", "YXY", "YZY", "ZXZ", "ZYZ",
]  # fmt: skip


@dataclass(frozen=True)
class Pose:
    """
    SE(3) pose with translation `t` (shape (3,)) and orientation `R` (scipy Rotation).
    Composition (matrix semantics):
      - Pose @ Pose = Pose
      - Pose @ Point = Point
    """

    R: Rot
    t: NDArray[np.floating]

    @property
    def x(self) -> float:
        return self.t[0]

    @property
    def y(self) -> float:
        return self.t[1]

    @property
    def z(self) -> float:
        return self.t[2]

    # ---- Basics ----
    @classmethod
    def identity(cls) -> Self:
        return cls(Rot.identity(), np.zeros(3, dtype=float))

    # ---- Constructors (with overloads to accept one concatenated array) ----

    @classmethod
    def from_xyz(cls, xyz: ArrayLike) -> Self:
        """
        Instanciate a pose without rotation.
        """
        if isinstance(xyz, (list, tuple)):
            assert len(xyz) == 3, f"xyz must be length 3, got {len(xyz)}"
        if isinstance(xyz, np.ndarray):
            assert xyz.shape == (3,) or xyz.shape == (1, 3), f"xyz must be shape (3,) or (1,3), got {xyz.shape}"
        return cls(Rot.identity(), np.asarray(xyz, dtype=float).reshape(3))

    @overload
    @classmethod
    def from_xyz_rotvec(cls, xyz: ArrayLike, rotvec: ArrayLike) -> Self: ...
    @overload
    @classmethod
    def from_xyz_rotvec(cls, xyz_rotvec: ArrayLike, /) -> Self: ...
    @classmethod
    def from_xyz_rotvec(cls, xyz: ArrayLike, rotvec: ArrayLike | None = None) -> Self:
        """
        Accepts:
          - (xyz, rotvec) -> lengths 3+3
          - single array/list of length 6 -> [x,y,z, rx,ry,rz]
        """
        if rotvec is None:
            arr = np.asarray(xyz, dtype=float).reshape(6)
            t = arr[:3]
            r = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            r = np.asarray(rotvec, dtype=float).reshape(3)
        return cls(Rot.from_rotvec(r), t)

    @overload
    @classmethod
    def from_xyz_euler(cls, xyz: ArrayLike, euler: ArrayLike, *, seq: str, degrees: bool = False) -> Self: ...
    @overload
    @classmethod
    def from_xyz_euler(cls, xyz_euler: ArrayLike, /, *, seq: str, degrees: bool = False) -> Self: ...
    @classmethod
    def from_xyz_euler(
        cls,
        xyz: ArrayLike,
        euler: ArrayLike | None = None,
        *,
        seq: str,
        degrees: bool = False,
    ) -> Self:
        """
        Accepts:
          - (xyz, euler) -> 3+3
          - single length-6 array -> [x,y,z, e1,e2,e3] in given `seq`
        """
        if euler is None:
            arr = np.asarray(xyz, dtype=float).reshape(6)
            t = arr[:3]
            e = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            e = np.asarray(euler, dtype=float).reshape(3)
        return cls(Rot.from_euler(seq, e, degrees=degrees), t)

    @overload
    @classmethod
    def from_xyz_quat(cls, xyz: ArrayLike, quat: ArrayLike, *, order: Literal["xyzw", "wxyz"] = "xyzw") -> Self: ...
    @overload
    @classmethod
    def from_xyz_quat(cls, xyz_quat: ArrayLike, /, *, order: Literal["xyzw", "wxyz"] = "xyzw") -> Self: ...
    @classmethod
    def from_xyz_quat(
        cls,
        xyz: ArrayLike,
        quat: ArrayLike | None = None,
        *,
        order: Literal["xyzw", "wxyz"] = "xyzw",
    ) -> Self:
        """
        Accepts:
          - (xyz, quat) -> 3 + 4
          - single length-7 array -> [x,y,z, q*] in given `order`
        """
        if quat is None:
            arr = np.asarray(xyz, dtype=float).reshape(7)
            t = arr[:3]
            q = arr[3:]
        else:
            t = np.asarray(xyz, dtype=float).reshape(3)
            q = np.asarray(quat, dtype=float).reshape(4)
        if order == "wxyz":
            q = np.array([q[1], q[2], q[3], q[0]], dtype=float)
        return cls(Rot.from_quat(q), t)  # SciPy expects xyzw

    @classmethod
    def from_RT(cls, R: ArrayLike, t: ArrayLike) -> Self:
        return cls(
            Rot.from_matrix(np.asarray(R, dtype=float).reshape(3, 3)),
            np.asarray(t, dtype=float).reshape(3),
        )

    @classmethod
    def from_SE3(cls, T: ArrayLike) -> Self:
        T = np.asarray(T, dtype=float).reshape(4, 4)
        return cls.from_RT(T[:3, :3], T[:3, 3])

    # ---- Converters ----
    def as_xyz_rotvec(self) -> NDArray[np.floating]:
        return np.hstack((self.t, self.R.as_rotvec()))

    def as_xyz_euler(self, seq: _RotAxisSeq, degrees: bool = False) -> NDArray[np.floating]:
        return np.hstack((self.t, self.R.as_euler(seq, degrees=degrees)))

    def as_xyz_quat(self, order: Literal["xyzw", "wxyz"] = "xyzw") -> NDArray[np.floating]:
        q = self.R.as_quat()  # xyzw
        if order == "wxyz":
            q = np.array([q[3], q[0], q[1], q[2]])
        return np.hstack((self.t, q))

    def as_RT(self) -> Tuple[NDArray[np.floating], NDArray[np.floating]]:
        return self.R.as_matrix(), self.t.copy()

    def as_SE3(self) -> NDArray[np.floating]:
        T = np.eye(4)
        T[:3, :3] = self.R.as_matrix()
        T[:3, 3] = self.t
        return T

    # ---- Core ops ----
    @overload
    def __matmul__(self, other: Self) -> Self: ...
    @overload
    def __matmul__(self, other: NDArray[np.floating]) -> NDArray[np.floating]: ...
    @overload
    def __matmul__(self, other: Rot) -> Self: ...

    def __matmul__(self, other):
        # Compose with another Pose
        if isinstance(other, Pose):
            R_new = self.R * other.R
            t_new = self.t + self.R.apply(other.t)
            return type(self)(R_new, t_new)
        if isinstance(other, Rot):
            return type(self)(self.R * other, self.t)

        M = np.asarray(other, dtype=float)
        # Pose @ 4x4 SE(3) numpy matrix -> Pose
        if M.shape == (4, 4):
            return self.as_SE3() @ M
        # Pose @ 1x4 or 4x1 homogeneous numpy vector -> homogeneous vector
        if M.shape == (4,) or M.shape == (1, 4):
            return (self.as_SE3() @ M.T).T
        return NotImplemented

    def __rmatmul__(self, left: NDArray[np.floating]) -> Self:
        # 4x4 SE(3) matrix @ Pose -> Pose
        M = np.asarray(left, dtype=float)
        if M.shape == (4, 4):
            T = M @ self.as_SE3()
            return type(self).from_SE3(T)
        return NotImplemented

    def __array_ufunc__(self, ufunc, method, *inputs, **kwargs):
        # Handle numpy ufuncs, particularly matmul
        if ufunc is np.matmul and method == "__call__":
            if len(inputs) == 2:
                left, right = inputs
                if right is self:
                    # left @ self
                    return self.__rmatmul__(left)
                elif left is self:
                    # self @ right
                    return self.__matmul__(right)
        return NotImplemented

    def inverse(self) -> Self:
        R_inv = self.R.inv()
        return type(self)(R_inv, -R_inv.apply(self.t))

    # Apply to numpy array(s) of points or vectors
    def apply(self, pts: ArrayLike) -> NDArray[np.floating]:
        P = np.asarray(pts, dtype=float)
        if P.ndim == 1 and P.shape == (3,):
            return self.R.apply(P) + self.t
        if P.ndim == 1 and P.shape == (4,):
            return (self.as_SE3() @ P.T).T
        if P.ndim == 2 and P.shape[1] == 3:
            return self.R.apply(P) + self.t
        if P.ndim == 2 and P.shape[1] == 4:
            return (self.as_SE3() @ P.T).T
        raise ValueError("pts must be shape (3,), (N,3), (4,), or (N,4).")

    # ---- SLERP (typed: scalar -> Pose, iterable -> list[Pose]) ----
    @overload
    def slerp(self: Self, other: Self, alpha: float) -> Self: ...
    @overload
    def slerp(self: Self, other: Self, alpha: Iterable[float]) -> List[Self]: ...
    def slerp(self, other, alpha):
        key_times = np.array([0.0, 1.0])
        rot_key = Rot.from_quat(np.vstack([self.R.as_quat(), other.R.as_quat()]))  # xyzw
        slerp_fn = Slerp(key_times, rot_key)

        is_scalar = isinstance(alpha, (int, float))
        alphas = np.array([alpha], dtype=float) if is_scalar else np.asarray(list(alpha), dtype=float)

        t_interp = (1.0 - alphas)[:, None] * self.t[None, :] + alphas[:, None] * other.t[None, :]
        R_interp = slerp_fn(alphas)

        poses = [type(self)(R_interp[i], t_interp[i]) for i in range(len(alphas))]
        return poses[0] if is_scalar else poses

    # ---------- Small utilities ----------
    def __sub__(self, other: Self) -> Self:
        """
        Compute the difference between two poses, expressed as a Pose in the frame of `other`.
        `diff = end - start` implies `end = start @ diff`, so right multiplication.
        """
        return other.inverse() @ self

    def with_translation(self, xyz: ArrayLike) -> Self:
        return type(self)(self.R, np.asarray(xyz, dtype=float).reshape(3))

    def with_rotation(self, R_new: Rot) -> Self:
        return type(self)(R_new, self.t)

    def copy(self) -> Self:
        return type(self)(Rot.from_quat(self.R.as_quat()), self.t.copy())  # safe copy

    def __eq__(self, other: object) -> bool:
        if not isinstance(other, Pose):
            return NotImplemented
        return np.allclose(self.t, other.t) and bool(self.R.approx_equal(other.R))


if __name__ == "__main__":
    start = Pose.from_xyz_rotvec([0, 5, 2, 0, 0, 0])
    start2 = Pose.from_xyz_rotvec([0, 5, 2, 0, 0, 0])
    floep = Pose.from_xyz_rotvec([0, 5, 2, 0, 0, 0.0001])
    assert start == start2
    end = Pose.from_xyz_rotvec([1, 1, 1, 0, 0, np.pi / 2])
    assert start != floep

    diff = end - start
    assert end == start @ diff
