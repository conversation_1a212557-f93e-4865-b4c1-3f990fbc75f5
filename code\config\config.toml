[boxes]
min_box_dimension = 0.09 # 9 cm

[robot]
gripper_size = [0.299, 0.129] # length, width
min_z_angle = -20
max_z_angle = 200

[robot.opcua]
ip = "*************"
port = 16448
namespace = 5

[conveyor]
ip_addresses = [
  "*************",
  "*************",
] # In order: upstream → downstream


# ================================= #
# Robot - conveyor calibration part #
# ================================= #

[conveyor_robot_calibration.robot_poses]
# zone.0 = most upstream zone
# os = opperator side, left when looking from upstream to downstream
# ds = drive side, right when looking from upstream to downstream
# [ x,  y,  z,  rx,  ry,  rz]
# [mm, mm, mm, deg, deg, deg]
zone.0.os = []
zone.0.ds = []
zone.1.os = []
zone.1.ds = [1275.622, 172.690, -1176.009, 179.3060, 0.3490, 89.9496] # not precise in x-direction yet, as there is no good sensor
zone.2.os = [1959.314, -546.341, -1165.978, 179.3064, 0.3500, 89.9505]
zone.2.ds = [1273.233, -529.979, -1179.884, 179.3055, 0.3521, 90.0740]
zone.3.os = []
zone.3.ds = []

[conveyor_robot_calibration.offsets]
pick_offset = [0.0, 0.0, -0.01, 0.0, 0.0, 0.0] # [x, y, z, rx, ry, rz], [m, m, m, deg, deg, deg] in base frame
zone.0 = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] # [x, y, z, rx, ry, rz], [m, m, m, deg, deg, deg] in zone frame
zone.1 = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] # [x, y, z, rx, ry, rz], [m, m, m, deg, deg, deg] in zone frame
zone.2 = [-0.009, 0.0, 0.0, 0.0, 0.0, 0.0] # [x, y, z, rx, ry, rz], [m, m, m, deg, deg, deg] in zone frame
zone.3 = [0.0, 0.0, 0.0, 0.0, 0.0, 0.0] # [x, y, z, rx, ry, rz], [m, m, m, deg, deg, deg] in zone frame

# ============================================================ #
# Camera - conveyor calibration part (to be filled in by user) #
# ============================================================ #

[camera.roi]
roi_height = 0.45    # this will be the top side of the ROI
deadzone_above_conveyor = 0.09 # this will be the bottom side of the ROI

[camera_conveyor_calibration.markers]
marker_margins = 0.008  # the ROI will be moved this distance towards the driver side
marker_thickness = 0.002       # the ROI will be moved this distance down after aruco measurement

[camera_conveyor_calibration.markers.ids]
upstream_marker = 7
width_marker = 3
downstream_marker = 6


################################ DO NOT EDIT BELOW THIS LINE ##############################

# Values below are calculated automatically by running calibration/camera_conveyor_calibration.py
[camera_conveyor_calibration.calibration]
pose = [[0.007655731157341632, 0.9998145958858335, 0.017668153103652925, 0.4647026439390979], [0.9998998984654797, -0.007443750974369008, -0.012032606540375869, 0.34714646433965496], [-0.011898858313738518, 0.017758502895210297, -0.9997715002668114, 1.2631583659143095], [0.0, 0.0, 0.0, 1.0]]
roi = [1.001955688402792, 0.8054575678610976]
