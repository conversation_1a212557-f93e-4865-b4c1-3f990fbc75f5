import asyncio

from pose import Pose
from robot.yaskawa import build_production as build_robot


async def main():
    robot = build_robot()
    async with robot.connect():
        await robot.touch_vertically(
            Pose.from_xyz_euler(
                [
                    1.6392679880894896,
                    -0.5035108977610173,
                    -0.9581161213308446,
                    179.61998712702928,
                    -0.8158019484912766,
                    -0.1252907616810101,
                ],
                seq="xyz",
                degrees=True,
            ),
            speed=0.01,
        )


if __name__ == "__main__":
    asyncio.run(main())
