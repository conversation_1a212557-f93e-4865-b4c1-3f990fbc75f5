from pprint import pprint
from typing import Generator, Optional

import numpy as np
import open3d as o3d
from loguru import logger
import tomlkit
from box_top.box_top_detector_protocol import BoxTopDetectorProtocol
from camera.camera_realsense import CameraProtocol, build_production as build_camera
from scipy.spatial.transform import Rotation as R
from collections import deque

from box_top import BoxTop
from box_top.visualizer import NoOpVisualizer, VisualizerProtocol
from pose import Pose


def apply_voxel_clustering(
    pcd: o3d.geometry.PointCloud, scan_line_x: float, voxel_size: float = 0.1
) -> o3d.geometry.PointCloud:
    """
    Apply voxel-based clustering to keep only points connected to the scan line.

    Args:
        pcd: Input point cloud
        scan_line_x: X coordinate of the scan line
        voxel_size: Size of voxel grid (default 0.05)

    Returns:
        Filtered point cloud containing only connected points
    """
    points = np.asarray(pcd.points)

    if len(points) == 0:
        return pcd

    # Create voxel grid by discretizing points
    voxel_coords = np.floor(points / voxel_size).astype(int)

    # Create a mapping from voxel coordinates to point indices
    voxel_to_points = {}
    for i, voxel_coord in enumerate(voxel_coords):
        voxel_key = tuple(voxel_coord)
        if voxel_key not in voxel_to_points:
            voxel_to_points[voxel_key] = []
        voxel_to_points[voxel_key].append(i)

    # Find the voxel closest to the scan line
    scan_line_voxel_x = int(np.floor(scan_line_x / voxel_size))

    # Find all voxels and their distances to the scan line
    voxel_keys = list(voxel_to_points.keys())
    if not voxel_keys:
        return pcd

    # Find the voxel with x coordinate closest to scan_line_voxel_x
    closest_voxel = None
    min_distance = float("inf")

    for voxel_key in voxel_keys:
        x_distance = abs(voxel_key[0] - scan_line_voxel_x)
        if x_distance < min_distance:
            min_distance = x_distance
            closest_voxel = voxel_key

    if closest_voxel is None:
        return pcd

    logger.trace(f"Scan line voxel x: {scan_line_voxel_x}, closest voxel: {closest_voxel}, distance: {min_distance}")

    # Perform BFS to find all connected voxels (26-connectivity in 3D)
    connected_voxels = set()
    queue = deque([closest_voxel])
    connected_voxels.add(closest_voxel)

    # Define 26-connectivity offsets (all combinations of -1, 0, 1 for x, y, z except (0,0,0))
    offsets = []
    for dx in [-1, 0, 1]:
        for dy in [-1, 0, 1]:
            for dz in [-1, 0, 1]:
                if dx != 0 or dy != 0 or dz != 0:
                    offsets.append((dx, dy, dz))

    while queue:
        current_voxel = queue.popleft()

        # Check all 26 neighbors
        for offset in offsets:
            neighbor = (
                current_voxel[0] + offset[0],
                current_voxel[1] + offset[1],
                current_voxel[2] + offset[2],
            )

            # If neighbor exists in our voxel grid and hasn't been visited
            if neighbor in voxel_to_points and neighbor not in connected_voxels:
                connected_voxels.add(neighbor)
                queue.append(neighbor)

    logger.trace(f"Found {len(connected_voxels)} connected voxels out of {len(voxel_to_points)} total voxels")

    # Collect all point indices from connected voxels
    connected_point_indices = []
    for voxel_key in connected_voxels:
        connected_point_indices.extend(voxel_to_points[voxel_key])

    # Create filtered point cloud
    if connected_point_indices:
        filtered_pcd = pcd.select_by_index(connected_point_indices)
        logger.trace(
            f"Filtered point cloud: {len(connected_point_indices)} points out of {len(points)} original points"
        )
        return filtered_pcd
    else:
        logger.trace("No connected points found, returning empty point cloud")
        return o3d.geometry.PointCloud()


def get_box_top(
    pcd,
    visualizer: Optional[VisualizerProtocol] = None,
    scan_line_x: Optional[float] = None,
):
    points = np.asarray(pcd.points)

    if len(points) < 10:
        logger.trace(f"Too few points in point cloud: {len(points)}")
        if visualizer is not None:
            visualizer.step()
        return None

    # Apply voxel clustering if scan_line_x is provided
    if scan_line_x is not None:
        logger.trace(f"Applying voxel clustering with scan line x: {scan_line_x:.3f}")
        pcd = apply_voxel_clustering(pcd, scan_line_x)
        points = np.asarray(pcd.points)

        if len(points) < 10:
            logger.trace(f"Too few points after clustering: {len(points)}")
            if visualizer is not None:
                visualizer.step()
            return None

    # Get top (high Z value) of box by taking 90th percentile of Z values
    z_values = points[:, 2]
    try:
        top_box_z = float(np.percentile(z_values, 90))
    except IndexError:
        logger.error(f"Failed to compute top of box (90th percentile Z), got {len(z_values)} points")
        return None
    logger.trace(f"Top of box (90th percentile Z): {top_box_z:.3f} mm")

    # Isolate top of box
    pcd = pcd.crop(
        o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(-np.inf, -np.inf, top_box_z - 0.1),  # pyright: ignore[reportArgumentType]
            max_bound=(np.inf, np.inf, top_box_z + 0.1),  # pyright: ignore[reportArgumentType]
        )
    )
    points = np.asarray(pcd.points)
    logger.trace(f"Min and max Z after cropping: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm")

    # Remove single points (noise)
    _, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=0.5)
    # cl, ind = pcd.remove_radius_outlier(nb_points=30, radius=0.01)
    outlier_cloud = pcd.select_by_index(ind, invert=True)
    outlier_cloud.paint_uniform_color([1, 0, 0])  # pyright: ignore[reportArgumentType]
    pcd = pcd.select_by_index(ind)

    try:
        # Convert point cloud to tensor for guaranteed minimal oriented bounding box
        points_tensor = o3d.core.Tensor(np.asarray(pcd.points), dtype=o3d.core.float64)

        # Use tensor-based approach with MINIMAL_JYLANKI method for guaranteed minimal OBB
        obb_tensor = o3d.t.geometry.OrientedBoundingBox.create_from_points(
            points_tensor,
            robust=False,
            method=o3d.t.geometry.MINIMAL_JYLANKI,
        )

        # Convert back to legacy format for compatibility with existing code
        obb = obb_tensor.to_legacy()
    except RuntimeError as e:
        logger.error(f"Failed to compute OBB: {e}")
        return None

    logger.trace(f"OBB center: {obb.center}")
    logger.trace(f"OBB extent: {obb.extent}")
    logger.trace(f"OBB rotation matrix: {obb.R}")
    logger.trace(f"OBB volume: {obb.volume()}")

    # We want to have the longest axis of the OBB be the length: the first axis.
    # The second axis should be the width, and the third axis should be the height.
    # The shortest axis of the OBB is the height.
    obb_extent = obb.extent.copy()
    obb_R = obb.R.copy()
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    if obb_extent[1] < obb_extent[2]:
        obb_extent = np.array([obb_extent[0], obb_extent[2], obb_extent[1]])
        obb_R = obb_R @ np.array([[1, 0, 0], [0, 0, 1], [0, -1, 0]])
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    logger.trace(f"OBB extent after sorting: {obb_extent}")

    # Get rotation angles from rotation matrix
    r = R.from_matrix(obb_R)
    angles = r.as_euler("zxy", degrees=True)
    logger.trace(f"z rotation: {angles[0]:.3f} deg, x rotation: {angles[1]:.3f} deg, y rotation: {angles[2]:.3f} deg")

    if visualizer is not None:
        # Use the injected visualizer
        visualizer.update(pcd, obb, outlier_cloud)

    return BoxTop(Pose(r, obb.center), obb_extent, top_box_z)  # pyright: ignore[reportArgumentType]


class BoxTopDetector(BoxTopDetectorProtocol):
    """
    Box top detector class that processes point clouds from a camera to detect box top positions.
    Uses dependency injection for the camera and visualizer implementations.
    """

    def __init__(self, camera: CameraProtocol, visualizer: Optional[VisualizerProtocol] = None):
        """
        Initialize the BoxTopDetector with a camera and optional visualizer.

        Args:
            camera: Camera implementation following CameraProtocol
            visualizer: Optional visualizer implementation following VisualizerProtocol
        """
        with open("config/config.toml", "r") as f:
            config = tomlkit.load(f)
        self.camera = camera
        self._should_stop = False
        transorm_matrix = np.array(config["camera_conveyor_calibration"]["calibration"]["pose"].unwrap())  # type: ignore
        self.transform: Pose = Pose.from_SE3(transorm_matrix)
        roi_x_y = config["camera_conveyor_calibration"]["calibration"]["roi"].unwrap()  # type: ignore
        self.ROI = o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(0, 0, config["camera"]["roi"]["deadzone_above_conveyor"].unwrap()),  # type: ignore
            max_bound=(roi_x_y[0], roi_x_y[1], config["camera"]["roi"]["roi_height"].unwrap()),  # type: ignore
        )
        self.min_box_dimension = config["boxes"]["min_box_dimension"].unwrap()  # type: ignore

        # Extract scan line x coordinate from transform matrix
        self.scan_line_x = self.transform.x  # x translation from transform matrix

        # Initialize visualizer if provided
        if visualizer is None:
            self.visualizer = NoOpVisualizer()
        else:
            self.visualizer = visualizer

    def detect_box_tops(self) -> Generator[Optional[BoxTop], None, None]:
        """
        Start box top detection and yield BoxTop objects as a stream.

        Yields:
            BoxTop objects or None if detection fails
        """
        with self.camera, self.visualizer:
            while not self._should_stop:
                pcd = self.camera.get_pointcloud()
                if pcd is None:
                    yield None
                    continue

                # Apply calibration
                pcd = pcd.transform(self.transform.as_SE3()).crop(self.ROI)  # type: ignore

                box_top = get_box_top(pcd, visualizer=self.visualizer, scan_line_x=self.scan_line_x)

                # Check if visualizer wants to stop (e.g., escape key pressed)
                if self.visualizer.should_stop():
                    logger.trace("Visualizer requested stop")
                    self._should_stop = True

                if box_top is None:
                    continue
                if box_top.obb_extent[0] < self.min_box_dimension or box_top.obb_extent[1] < self.min_box_dimension:
                    logger.debug(f"Box too small: {box_top.obb_extent}")
                    continue

                yield box_top

    def detect_single(self) -> Optional[BoxTop]:
        """
        Detect a single box top from the current camera frame.

        Returns:
            BoxTop object or None if detection fails
        """
        pcd = self.camera.get_pointcloud()
        if pcd is None:
            return None

        # Apply calibration
        pcd = pcd.transform(self.transform.as_SE3()).crop(self.ROI)  # type: ignore

        return get_box_top(pcd, visualizer=self.visualizer, scan_line_x=self.scan_line_x)


if __name__ == "__main__":
    from box_top.visualizer import Open3DVisualizer

    camera = build_camera()

    # Create visualizer for demonstration
    visualizer = Open3DVisualizer()
    box_top_detector = BoxTopDetector(camera, visualizer=visualizer)

    try:
        for box_top in box_top_detector.detect_box_tops():
            if box_top is not None:
                pprint(box_top)
    except KeyboardInterrupt:
        logger.info("Stopping detection...")
