import asyncio
from functools import cache
from typing import Literal

import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import tomlkit
from loguru import logger
from numpy.typing import NDArray

from box_top import BoxTop, ConveyorBox
from calibration.points_to_transform import get_pose_from_three_points
from pose import Pose, Rot
from robot.constrain_angle import constrain_z_angle
from robot.yaskawa import build_production as build_robot
from utilities.pose_visualizer import vis_multiple_poses


def read_as_array(config_element) -> np.ndarray:
    return np.array(config_element.unwrap())  # type: ignore


@cache
def get_config():
    with open("config/config.toml", "r") as f:
        return tomlkit.load(f)


@cache
def get_robot_pose_t(zone: int, side: Literal["os", "ds"]) -> NDArray[np.floating]:
    config = get_config()
    return read_as_array(config["conveyor_robot_calibration"]["robot_poses"]["zone"][f"{zone}"][side])[:3] * 0.001  # type: ignore


def get_zone_offset(zone: int) -> np.ndarray:
    config = get_config()
    return read_as_array(config["conveyor_robot_calibration"]["offsets"]["zone"][f"{zone}"])  # type: ignore


@cache
def get_zone_pose(zone) -> Pose:
    origin = get_robot_pose_t(zone, "ds")
    if zone == 2 or zone == 3:
        x_point = get_robot_pose_t(zone - 1, "ds")
        x_point = 2 * origin - x_point  # mirror x_point around origin: new_x_point = origin - (x_point - origin)
    else:
        x_point = get_robot_pose_t(zone + 1, "ds")
    y_point = get_robot_pose_t(zone, "os")
    pose = get_pose_from_three_points(origin, x_point, y_point)
    # correct for gripper size
    gripper_size = get_gripper_size()
    pose = pose @ Pose.from_xyz([gripper_size[0] / 2, -gripper_size[1] / 2, 0.0])
    # apply zone offset
    pose = pose @ Pose.from_xyz_euler(get_zone_offset(zone), seq="xyz", degrees=True)
    return pose


@cache
def get_zone_pose_rotation_deg(zone) -> np.ndarray:
    config = get_config()
    return read_as_array(config["conveyor_robot_calibration"]["robot_poses"]["zone"][f"{zone}"]["ds"])[3:6]  # type: ignore


@cache
def get_gripper_size() -> np.ndarray:
    config = get_config()
    return read_as_array(config["robot"]["gripper_size"])  # type: ignore


@cache
def get_pick_offset() -> np.ndarray:
    config = get_config()
    return read_as_array(config["conveyor_robot_calibration"]["pick_offset"])  # type: ignore


def get_box_in_zone_pick_pose(zone: int, box: ConveyorBox) -> Pose:
    # the box is touching the sensor = touching the plane x=0
    obb_top = o3d.geometry.OrientedBoundingBox(
        center=[0, 0, 0],  # pyright: ignore[reportArgumentType]
        R=box.top.obb_center.R.as_matrix(),  # pyright: ignore[reportArgumentType]
        extent=box.top.obb_extent,  # pyright: ignore[reportArgumentType]
    )
    aabb_top = obb_top.get_axis_aligned_bounding_box()

    # # draw open3d box (obb), abb and origin
    # o3d.visualization.draw_geometries(  # type: ignore
    #     [
    #         obb_top,
    #         aabb_top,
    #         o3d.geometry.TriangleMesh.create_coordinate_frame(),
    #     ]
    # )

    x_position = -(aabb_top.max_bound[0] - aabb_top.min_bound[0]) / 2
    y_position = box.top.obb_center.y
    z_position = box.top.z_position
    logger.debug(f"Box position: x={x_position:.3f}, y={y_position:.3f}, z={z_position:.3f}")

    box_pick_pose_in_zone_without_rotation = Pose.from_xyz([x_position, y_position, z_position])
    # box_pick_pose_in_zone = Pose(box.top.obb_center.R, np.array([x_position, y_position, z_position]))
    # discard x and y rotation
    box_pick_pose_in_zone = Pose(
        Rot.from_euler("z", box.top.obb_center.R.as_euler("xyz", degrees=True)[2], degrees=True),
        np.array([x_position, y_position, z_position]),
    )

    # vis_multiple_poses(
    #     [box_pick_pose_in_zone_without_rotation, box_pick_pose_in_zone],
    #     labels=["box_pick_pose_in_zone_without_rotation", "box_pick_pose_in_zone"],
    #     show_origin=True,
    #     title="box_pick_pose_in_zone",
    # )
    # plt.show()

    print(f"Box pick pose in zone: {box_pick_pose_in_zone.as_xyz_euler('xyz', degrees=True).tolist()}")
    print(f"Zone pose: {get_zone_pose(zone).as_xyz_euler('xyz', degrees=True).tolist()}")
    zone_transform = get_zone_pose(zone)
    box_pick_pose_without_original_rotation = zone_transform @ box_pick_pose_in_zone_without_rotation
    box_pick_pose = zone_transform @ box_pick_pose_in_zone
    print(f"Box pick pose in world: {box_pick_pose.as_xyz_euler('xyz', degrees=True).tolist()}")

    # vis_multiple_poses(
    #     [box_pick_pose_without_original_rotation, box_pick_pose],
    #     labels=["box_pick_pose_without_original_rotation", "box_pick_pose"],
    #     show_origin=True,
    #     title="box_pick_pose_in_world",
    # )
    # plt.show()

    # gripper = 180° around x axis
    gripper_rotation = Rot.from_euler("x", 180, degrees=True)
    box_pick_pose = box_pick_pose @ gripper_rotation

    # gripper_rotation = Rot.from_euler("xyz", get_zone_pose_rotation_deg(zone), degrees=True)
    # # # rotation[2] += box.top.obb_center.R.as_euler("zyx", degrees=True)[0]
    # # # rotation[2] = constrain_angle(rotation[2])

    # # # box_pick_pose = box_pick_pose @ Pose.from_xyz_euler([0, 0, 0], rotation, seq="xyz", degrees=True)
    # box_pick_pose = box_pick_pose_without_original_rotation @ gripper_rotation

    # # add box z rotation (inverted, z points down for tool)
    # logger.debug(f"Box z rotation: {box.top.obb_center.R.as_euler('xyz', degrees=True)[0]:.3f} deg")
    # logger.debug(f"Gripper z rotation: {box_pick_pose.R.as_euler('xyz', degrees=True)[0]:.3f} deg")
    # box_pick_pose = box_pick_pose @ Rot.from_euler(
    #     "z", -box.top.obb_center.R.as_euler("zyx", degrees=True)[0], degrees=True
    # )
    # logger.debug(f"Combined gripper z rotation: {box_pick_pose.R.as_euler('zyx', degrees=True)[0]:.3f} deg")

    # # pick_offset = get_pick_offset()
    # # box_pick_pose = Pose.from_xyz_euler(pick_offset, seq="xyz", degrees=True) @ box_pick_pose

    # # constrain z angle
    # logger.debug(f"Gripper z rotation before constrain: {box_pick_pose.R.as_euler('xyz', degrees=True)[2]:.3f} deg")
    box_pick_pose = constrain_z_angle(box_pick_pose)
    # logger.debug(f"Gripper z rotation after constrain: {box_pick_pose.R.as_euler('xyz', degrees=True)[2]:.3f} deg")

    # vis_multiple_poses(
    #     [box_pick_pose_without_original_rotation, box_pick_pose],
    #     labels=["box_pick_pose_without_original_rotation", "box_pick_pose"],
    #     show_origin=True,
    #     title="box_pick_pose_in_world",
    # )
    # plt.show()
    assert np.allclose(box_pick_pose_without_original_rotation.t, box_pick_pose.t)

    return box_pick_pose


async def main():
    Rotation = Rot
    array = np.array
    box = ConveyorBox(
        top=BoxTop(
            obb_center=Pose(
                R=Rotation.from_matrix(
                    array(
                        [
                            [-0.01713633, 0.99912937, 0.03803747],
                            [0.99984281, 0.01729685, -0.00389499],
                            [-0.00454953, 0.03796474, -0.99926872],
                        ]
                    )
                ),
                t=array([0.50580829, 0.40663034, 0.32447836]),
            ),
            obb_extent=array([0.60308349, 0.22339095, 0.01428949]),
            z_position=0.3289221947075316,
        ),
        obb_extent=array([0.60308349, 0.22339095, 0.32892219]),
        volume=0.0,
    )
    result = get_box_in_zone_pick_pose(2, box)
    print(result.as_xyz_euler("xyz", degrees=True).tolist())

    # go = input("Go?")
    # if go != "y":
    #     exit()

    # robot = build_robot()
    # async with robot.connect():
    #     await robot.touch_vertically(
    #         result,
    #         speed=0.01,
    #     )


if __name__ == "__main__":
    asyncio.run(main())
